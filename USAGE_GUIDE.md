# Instagram Bulk Downloader - Usage Guide

## Quick Start

### 1. First Time Setup
```bash
# Run setup to create directories and example files
python3 ig_downloader.py --setup
```

### 2. Add Your Instagram URLs
Edit the `urls.txt` file and add your Instagram URLs:
```
# Instagram URLs - one per line
https://www.instagram.com/p/YOUR_POST_ID_1/
https://www.instagram.com/reel/YOUR_REEL_ID_2/
https://www.instagram.com/tv/YOUR_TV_ID_3/
```

### 3. Download Content
```bash
# Download from URLs file
python3 ig_downloader.py -f urls.txt

# Or download single URL
python3 ig_downloader.py "https://www.instagram.com/p/YOUR_POST_ID/"
```

## Detailed Usage Examples

### Command Line Options

```bash
# Setup directories and create example files
python3 ig_downloader.py --setup

# Download single Instagram post
python3 ig_downloader.py "https://www.instagram.com/p/ABC123/"

# Download multiple posts from command line
python3 ig_downloader.py "URL1" "URL2" "URL3"

# Download from file (recommended for bulk downloads)
python3 ig_downloader.py -f urls.txt

# Download from custom file
python3 ig_downloader.py -f my_instagram_urls.txt

# Get help
python3 ig_downloader.py --help
```

### Using Batch/Shell Scripts

**Windows (run.bat):**
```cmd
run.bat --setup
run.bat -f urls.txt
run.bat "https://www.instagram.com/p/ABC123/"
```

**Unix/Linux/Mac (run.sh):**
```bash
./run.sh --setup
./run.sh -f urls.txt
./run.sh "https://www.instagram.com/p/ABC123/"
```

## Supported URL Types

The tool supports these Instagram URL formats:

- **Posts**: `https://www.instagram.com/p/POST_ID/`
- **Reels**: `https://www.instagram.com/reel/REEL_ID/`
- **IGTV**: `https://www.instagram.com/tv/TV_ID/`

## Output Structure

After downloading, your files will be organized as follows:

```
downloads/
├── images/
│   ├── POST_ID_1/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── POST_ID_2/
│       └── single_image.png
├── videos/
│   ├── REEL_ID_1/
│   │   └── reel_video.mp4
│   └── TV_ID_1/
│       └── igtv_video.mp4
└── metadata/
    ├── POST_ID_1.json
    ├── POST_ID_2.json
    ├── REEL_ID_1.json
    └── TV_ID_1.json
```

## Features

### ✅ What Works
- Downloads public Instagram posts, reels, and IGTV videos
- No login required
- Automatic file organization into images/videos folders
- Metadata extraction (title, description, likes, views, etc.)
- Bulk processing with progress tracking
- Error handling and retry logic
- Cross-platform support (Windows, Mac, Linux)

### ❌ Limitations
- Only works with **public** content (private accounts require login)
- Stories may not work consistently
- Rate limiting may occur with very large bulk downloads
- Some geo-restricted content may not be accessible

## Troubleshooting

### Common Issues

**1. "No module named 'yt_dlp'" or similar import errors**
```bash
pip3 install -r requirements.txt
```

**2. Downloads failing with "Private account" or "Login required"**
- The content is from a private account
- Only public Instagram content can be downloaded without login

**3. "Command not found" errors**
- Make sure Python 3.7+ is installed
- Use `python3` instead of `python` on Mac/Linux
- On Windows, try `python` instead of `python3`

**4. Permission errors**
- Make sure you have write permissions in the directory
- Try running from a different directory

**5. SSL/Certificate errors**
- This is usually harmless (just a warning)
- Downloads should still work normally

### Getting Help

Run the test suite to verify everything is working:
```bash
python3 test_downloader.py
```

Check the tool's help:
```bash
python3 ig_downloader.py --help
```

## Advanced Configuration

You can modify `config.py` to customize:

- **Download directories**: Change where files are saved
- **File naming**: Modify how files are named
- **Retry settings**: Adjust retry attempts and delays
- **yt-dlp options**: Configure download quality and format
- **User agent**: Change the browser identification string

## Legal and Ethical Use

⚠️ **Important**: This tool is for educational and personal use only.

- Only download content you have permission to download
- Respect Instagram's Terms of Service
- Respect content creators' rights and copyrights
- Don't use this tool for commercial purposes without proper authorization
- Be respectful with download frequency to avoid overloading Instagram's servers

## Performance Tips

- For large bulk downloads, use the file method (`-f urls.txt`) rather than command line arguments
- Add delays between downloads by modifying the sleep time in the code
- Monitor your internet connection during large downloads
- Consider downloading during off-peak hours

## File Formats

The tool will download content in the best available quality:

- **Images**: JPG, PNG, WebP
- **Videos**: MP4, MOV, WebM
- **Metadata**: JSON format with post information

## Examples

### Example 1: Single Post Download
```bash
python3 ig_downloader.py "https://www.instagram.com/p/ABC123/"
```

### Example 2: Bulk Download
Create `my_urls.txt`:
```
https://www.instagram.com/p/ABC123/
https://www.instagram.com/reel/DEF456/
https://www.instagram.com/tv/GHI789/
```

Then run:
```bash
python3 ig_downloader.py -f my_urls.txt
```

### Example 3: Mixed Content Types
```bash
python3 ig_downloader.py \
  "https://www.instagram.com/p/POST123/" \
  "https://www.instagram.com/reel/REEL456/" \
  "https://www.instagram.com/tv/TV789/"
```

This tool provides a comprehensive solution for downloading Instagram content while respecting the platform's limitations and maintaining organized file structure.
