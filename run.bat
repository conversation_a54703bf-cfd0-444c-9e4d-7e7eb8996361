@echo off
echo Instagram Bulk Downloader
echo ========================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

REM Install dependencies if requirements.txt exists
if exist requirements.txt (
    echo Installing dependencies...
    pip install -r requirements.txt
)

REM Run the downloader
if "%1"=="" (
    echo Usage: run.bat [URLs or -f filename]
    echo Examples:
    echo   run.bat --setup
    echo   run.bat -f urls.txt
    echo   run.bat "https://www.instagram.com/p/EXAMPLE/"
    pause
) else (
    python ig_downloader.py %*
    pause
)
