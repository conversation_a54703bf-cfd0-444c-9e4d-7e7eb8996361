"""
Utility functions for Instagram Downloader
"""
import os
import re
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from urllib.parse import urlparse
import config


def create_directories():
    """Create necessary directories for downloads"""
    directories = [
        config.DOWNLOADS_DIR,
        config.IMAGES_DIR,
        config.VIDEOS_DIR,
        config.METADATA_DIR
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print(f"✓ Created directory structure in: {config.DOWNLOADS_DIR}")


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file system usage"""
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = re.sub(r'\s+', '_', filename)
    filename = filename.strip('._')
    
    # Limit length
    if len(filename) > config.MAX_FILENAME_LENGTH:
        name, ext = os.path.splitext(filename)
        filename = name[:config.MAX_FILENAME_LENGTH - len(ext)] + ext
    
    return filename


def extract_post_id(url: str) -> Optional[str]:
    """Extract Instagram post ID from URL"""
    for pattern in config.INSTAGRAM_PATTERNS:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None


def is_image_file(filepath: str) -> bool:
    """Check if file is an image"""
    ext = os.path.splitext(filepath)[1].lower()
    return ext in config.IMAGE_EXTENSIONS


def is_video_file(filepath: str) -> bool:
    """Check if file is a video"""
    ext = os.path.splitext(filepath)[1].lower()
    return ext in config.VIDEO_EXTENSIONS


def move_file_to_category(filepath: str, post_id: str) -> str:
    """Move downloaded file to appropriate category folder"""
    if not os.path.exists(filepath):
        return filepath
    
    filename = os.path.basename(filepath)
    
    if is_image_file(filepath):
        # Create subfolder for this post in images directory
        post_folder = os.path.join(config.IMAGES_DIR, post_id)
        Path(post_folder).mkdir(exist_ok=True)
        destination = os.path.join(post_folder, filename)
    elif is_video_file(filepath):
        # Create subfolder for this post in videos directory
        post_folder = os.path.join(config.VIDEOS_DIR, post_id)
        Path(post_folder).mkdir(exist_ok=True)
        destination = os.path.join(post_folder, filename)
    else:
        # Keep other files in downloads root
        destination = os.path.join(config.DOWNLOADS_DIR, filename)
    
    try:
        shutil.move(filepath, destination)
        return destination
    except Exception as e:
        print(f"Warning: Could not move {filepath} to {destination}: {e}")
        return filepath


def save_metadata(post_id: str, metadata: Dict) -> str:
    """Save post metadata to JSON file"""
    metadata_file = os.path.join(config.METADATA_DIR, f"{post_id}.json")
    
    # Add timestamp
    metadata['downloaded_at'] = datetime.now().isoformat()
    
    try:
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        return metadata_file
    except Exception as e:
        print(f"Warning: Could not save metadata for {post_id}: {e}")
        return ""


def load_urls_from_file(filepath: str) -> List[str]:
    """Load URLs from a text file"""
    urls = []
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    urls.append(line)
    except FileNotFoundError:
        print(f"Error: File {filepath} not found")
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
    
    return urls


def validate_instagram_url(url: str) -> bool:
    """Validate if URL is a valid Instagram post URL"""
    return any(re.search(pattern, url) for pattern in config.INSTAGRAM_PATTERNS)


def get_timestamp() -> str:
    """Get current timestamp string"""
    return datetime.now().strftime(config.TIMESTAMP_FORMAT)


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"
