#!/usr/bin/env python3
"""
Getting Started with Instagram Bulk Downloader
Interactive setup and guidance script
"""

import os
import sys
from pathlib import Path

def print_header():
    """Print welcome header"""
    print("=" * 60)
    print("🎯 INSTAGRAM BULK DOWNLOADER")
    print("=" * 60)
    print("Download Instagram posts, reels, and videos without login!")
    print("Automatically organizes content into folders.")
    print()

def check_dependencies():
    """Check if dependencies are installed"""
    print("📋 Checking dependencies...")
    
    try:
        import yt_dlp
        import requests
        import tqdm
        import colorama
        print("✅ All dependencies are installed!")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("\n🔧 To install dependencies, run:")
        print("   pip3 install -r requirements.txt")
        return False

def setup_directories():
    """Setup directory structure"""
    print("\n📁 Setting up directories...")
    
    try:
        from ig_downloader import main as ig_main
        import utils
        
        utils.create_directories()
        print("✅ Directory structure created!")
        return True
    except Exception as e:
        print(f"❌ Error setting up directories: {e}")
        return False

def show_usage_examples():
    """Show usage examples"""
    print("\n📖 USAGE EXAMPLES:")
    print("-" * 40)
    
    examples = [
        ("Setup (first time)", "python3 ig_downloader.py --setup"),
        ("Single URL", 'python3 ig_downloader.py "https://www.instagram.com/p/ABC123/"'),
        ("Multiple URLs", 'python3 ig_downloader.py "URL1" "URL2" "URL3"'),
        ("From file", "python3 ig_downloader.py -f urls.txt"),
        ("Get help", "python3 ig_downloader.py --help"),
    ]
    
    for description, command in examples:
        print(f"  {description}:")
        print(f"    {command}")
        print()

def show_url_format():
    """Show supported URL formats"""
    print("🔗 SUPPORTED URL FORMATS:")
    print("-" * 40)
    
    formats = [
        ("Posts", "https://www.instagram.com/p/POST_ID/"),
        ("Reels", "https://www.instagram.com/reel/REEL_ID/"),
        ("IGTV", "https://www.instagram.com/tv/TV_ID/"),
    ]
    
    for type_name, format_example in formats:
        print(f"  {type_name}: {format_example}")
    print()

def show_folder_structure():
    """Show output folder structure"""
    print("📂 OUTPUT FOLDER STRUCTURE:")
    print("-" * 40)
    
    structure = """
downloads/
├── images/
│   ├── POST_ID_1/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── POST_ID_2/
│       └── image.png
├── videos/
│   ├── REEL_ID_1/
│   │   └── video.mp4
│   └── TV_ID_1/
│       └── video.mp4
└── metadata/
    ├── POST_ID_1.json
    ├── POST_ID_2.json
    └── REEL_ID_1.json
"""
    print(structure)

def interactive_setup():
    """Interactive setup process"""
    print("🚀 INTERACTIVE SETUP:")
    print("-" * 40)
    
    # Ask if user wants to run setup
    response = input("Would you like to run the setup now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        print("\nRunning setup...")
        try:
            os.system("python3 ig_downloader.py --setup")
            print("✅ Setup completed!")
        except Exception as e:
            print(f"❌ Setup failed: {e}")
    
    # Ask about URLs
    print("\n" + "=" * 50)
    print("📝 ADDING INSTAGRAM URLs:")
    print("=" * 50)
    print("1. Edit 'urls.txt' file and add your Instagram URLs")
    print("2. One URL per line")
    print("3. Lines starting with # are comments")
    print()
    
    response = input("Would you like to open urls.txt for editing? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        # Try to open the file with default editor
        try:
            if sys.platform.startswith('darwin'):  # macOS
                os.system("open urls.txt")
            elif sys.platform.startswith('win'):   # Windows
                os.system("notepad urls.txt")
            else:  # Linux
                os.system("nano urls.txt || vim urls.txt || gedit urls.txt")
        except:
            print("Please manually edit the 'urls.txt' file")

def show_next_steps():
    """Show next steps"""
    print("\n🎯 NEXT STEPS:")
    print("-" * 40)
    
    steps = [
        "1. Add Instagram URLs to 'urls.txt'",
        "2. Run: python3 ig_downloader.py -f urls.txt",
        "3. Check the 'downloads' folder for your content",
        "4. Images will be in downloads/images/",
        "5. Videos will be in downloads/videos/",
        "6. Metadata will be in downloads/metadata/",
    ]
    
    for step in steps:
        print(f"  {step}")
    
    print("\n💡 TIP: Only public Instagram content can be downloaded!")
    print("💡 TIP: Run 'python3 test_downloader.py' to test the installation")

def main():
    """Main function"""
    print_header()
    
    # Check dependencies
    if not check_dependencies():
        return
    
    # Setup directories
    if not setup_directories():
        return
    
    # Show information
    show_usage_examples()
    show_url_format()
    show_folder_structure()
    
    # Interactive setup
    interactive_setup()
    
    # Show next steps
    show_next_steps()
    
    print("\n" + "=" * 60)
    print("🎉 You're all set! Happy downloading!")
    print("=" * 60)

if __name__ == "__main__":
    main()
