#!/bin/bash

echo "Instagram Bulk Downloader"
echo "========================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3.7+ from your package manager"
    exit 1
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "Installing dependencies..."
    pip3 install -r requirements.txt
fi

# Run the downloader
if [ $# -eq 0 ]; then
    echo "Usage: ./run.sh [URLs or -f filename]"
    echo "Examples:"
    echo "  ./run.sh --setup"
    echo "  ./run.sh -f urls.txt"
    echo "  ./run.sh \"https://www.instagram.com/p/EXAMPLE/\""
else
    python3 ig_downloader.py "$@"
fi
