#!/usr/bin/env python3
"""
Check Downloads Status
Analyzes what was downloaded vs what was requested
"""

import os
import sys
from pathlib import Path

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import utils
import config


def count_downloaded_files():
    """Count successfully downloaded files"""
    video_count = 0
    image_count = 0
    metadata_count = 0
    
    # Count videos
    if os.path.exists(config.VIDEOS_DIR):
        video_folders = [d for d in os.listdir(config.VIDEOS_DIR) if os.path.isdir(os.path.join(config.VIDEOS_DIR, d))]
        video_count = len(video_folders)
    
    # Count images
    if os.path.exists(config.IMAGES_DIR):
        image_folders = [d for d in os.listdir(config.IMAGES_DIR) if os.path.isdir(os.path.join(config.IMAGES_DIR, d))]
        image_count = len(image_folders)
    
    # Count metadata
    if os.path.exists(config.METADATA_DIR):
        metadata_files = [f for f in os.listdir(config.METADATA_DIR) if f.endswith('.json') and f != 'TEST123.json']
        metadata_count = len(metadata_files)
    
    return video_count, image_count, metadata_count


def analyze_downloads():
    """Analyze download results"""
    print("Instagram Download Analysis")
    print("=" * 40)
    
    # Load original URLs
    urls = utils.load_urls_from_file('urls.txt')
    total_requested = len(urls)
    
    print(f"Total URLs requested: {total_requested}")
    
    # Count downloads
    video_count, image_count, metadata_count = count_downloaded_files()
    
    print(f"\nDownload Results:")
    print(f"  Videos downloaded: {video_count}")
    print(f"  Images downloaded: {image_count}")
    print(f"  Metadata files: {metadata_count}")
    print(f"  Total successful: {video_count + image_count}")
    
    # Calculate success rate
    success_rate = ((video_count + image_count) / total_requested) * 100 if total_requested > 0 else 0
    print(f"  Success rate: {success_rate:.1f}%")
    
    # Check for error files
    error_log = os.path.join(config.DOWNLOADS_DIR, "failed_downloads.txt")
    error_report = os.path.join(config.DOWNLOADS_DIR, "error_report.txt")
    
    if os.path.exists(error_log):
        print(f"\n✓ Error log found: {error_log}")
    
    if os.path.exists(error_report):
        print(f"✓ Error report found: {error_report}")
    
    # Show some example downloads
    print(f"\nExample downloaded videos:")
    if os.path.exists(config.VIDEOS_DIR):
        video_folders = [d for d in os.listdir(config.VIDEOS_DIR) if os.path.isdir(os.path.join(config.VIDEOS_DIR, d))]
        for i, folder in enumerate(video_folders[:5]):
            folder_path = os.path.join(config.VIDEOS_DIR, folder)
            files = os.listdir(folder_path)
            if files:
                file_path = os.path.join(folder_path, files[0])
                file_size = os.path.getsize(file_path)
                print(f"  {folder}: {files[0]} ({utils.format_file_size(file_size)})")
    
    return total_requested, video_count + image_count


def create_failed_urls_file():
    """Create a file with URLs that might have failed"""
    urls = utils.load_urls_from_file('urls.txt')
    
    # Get list of successfully downloaded post IDs
    successful_ids = set()
    
    # From videos
    if os.path.exists(config.VIDEOS_DIR):
        video_folders = [d for d in os.listdir(config.VIDEOS_DIR) if os.path.isdir(os.path.join(config.VIDEOS_DIR, d))]
        successful_ids.update(video_folders)
    
    # From images
    if os.path.exists(config.IMAGES_DIR):
        image_folders = [d for d in os.listdir(config.IMAGES_DIR) if os.path.isdir(os.path.join(config.IMAGES_DIR, d))]
        successful_ids.update(image_folders)
    
    # Find potentially failed URLs
    failed_urls = []
    for url in urls:
        post_id = utils.extract_post_id(url)
        if post_id and post_id not in successful_ids:
            failed_urls.append(url)
    
    if failed_urls:
        failed_file = "failed_urls.txt"
        with open(failed_file, 'w', encoding='utf-8') as f:
            f.write("# URLs that may have failed to download\n")
            f.write("# You can retry these with: python3 ig_downloader.py -f failed_urls.txt\n\n")
            for url in failed_urls:
                f.write(f"{url}\n")
        
        print(f"\n📝 Created {failed_file} with {len(failed_urls)} potentially failed URLs")
        print(f"   Retry with: python3 ig_downloader.py -f {failed_file}")
        return failed_file
    else:
        print(f"\n✅ All URLs appear to have been processed successfully!")
        return None


def main():
    """Main function"""
    utils.create_directories()
    
    total_requested, total_successful = analyze_downloads()
    
    if total_successful < total_requested:
        print(f"\n⚠️  Some downloads may have failed ({total_requested - total_successful} missing)")
        failed_file = create_failed_urls_file()
        
        if failed_file:
            print(f"\nTo retry failed downloads:")
            print(f"  python3 ig_downloader.py -f {failed_file}")
    else:
        print(f"\n🎉 All downloads completed successfully!")
    
    print(f"\n📁 Your downloads are organized in:")
    print(f"   Videos: {config.VIDEOS_DIR}")
    print(f"   Images: {config.IMAGES_DIR}")
    print(f"   Metadata: {config.METADATA_DIR}")


if __name__ == "__main__":
    main()
