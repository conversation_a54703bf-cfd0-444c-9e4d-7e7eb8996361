#!/usr/bin/env python3
"""
Test script for Instagram Downloader
This script demonstrates the functionality without requiring actual Instagram URLs
"""

import os
import sys
from pathlib import Path

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import utils
import config
from ig_downloader import InstagramDownloader


def test_url_validation():
    """Test URL validation functionality"""
    print("Testing URL validation...")
    
    valid_urls = [
        "https://www.instagram.com/p/ABC123/",
        "https://instagram.com/reel/DEF456/",
        "https://www.instagram.com/tv/GHI789/",
    ]
    
    invalid_urls = [
        "https://youtube.com/watch?v=123",
        "https://facebook.com/post/123",
        "not_a_url",
        "",
    ]
    
    print("Valid URLs:")
    for url in valid_urls:
        is_valid = utils.validate_instagram_url(url)
        post_id = utils.extract_post_id(url)
        print(f"  ✓ {url} -> Valid: {is_valid}, Post ID: {post_id}")
    
    print("\nInvalid URLs:")
    for url in invalid_urls:
        is_valid = utils.validate_instagram_url(url)
        print(f"  ✗ {url} -> Valid: {is_valid}")


def test_file_operations():
    """Test file utility functions"""
    print("\nTesting file operations...")
    
    # Test filename sanitization
    test_filenames = [
        "normal_filename.jpg",
        "file with spaces.mp4",
        "file<>with|invalid*chars?.png",
        "very_long_filename_that_exceeds_the_maximum_length_limit_and_should_be_truncated_properly.jpg"
    ]
    
    print("Filename sanitization:")
    for filename in test_filenames:
        sanitized = utils.sanitize_filename(filename)
        print(f"  '{filename}' -> '{sanitized}'")
    
    # Test file type detection
    test_files = [
        "image.jpg",
        "video.mp4",
        "photo.png",
        "movie.avi",
        "document.txt"
    ]
    
    print("\nFile type detection:")
    for filename in test_files:
        is_image = utils.is_image_file(filename)
        is_video = utils.is_video_file(filename)
        print(f"  {filename} -> Image: {is_image}, Video: {is_video}")


def test_directory_structure():
    """Test directory creation and structure"""
    print("\nTesting directory structure...")
    
    # Check if directories exist
    directories = [
        config.DOWNLOADS_DIR,
        config.IMAGES_DIR,
        config.VIDEOS_DIR,
        config.METADATA_DIR
    ]
    
    for directory in directories:
        exists = os.path.exists(directory)
        print(f"  {directory} -> Exists: {exists}")


def test_urls_file():
    """Test loading URLs from file"""
    print("\nTesting URL file loading...")
    
    # Create a test URLs file
    test_urls_content = """# Test URLs file
# This is a comment

https://www.instagram.com/p/TEST123/
https://www.instagram.com/reel/TEST456/

# Another comment
https://www.instagram.com/tv/TEST789/
"""
    
    test_file = "test_urls.txt"
    with open(test_file, 'w') as f:
        f.write(test_urls_content)
    
    # Load URLs
    urls = utils.load_urls_from_file(test_file)
    print(f"Loaded {len(urls)} URLs from test file:")
    for url in urls:
        print(f"  - {url}")
    
    # Clean up
    os.remove(test_file)


def test_metadata_operations():
    """Test metadata saving functionality"""
    print("\nTesting metadata operations...")
    
    test_metadata = {
        'title': 'Test Post',
        'description': 'This is a test post description',
        'uploader': 'test_user',
        'upload_date': '20240101',
        'view_count': 1000,
        'like_count': 50,
        'url': 'https://www.instagram.com/p/TEST123/'
    }
    
    post_id = "TEST123"
    metadata_file = utils.save_metadata(post_id, test_metadata)
    
    if metadata_file and os.path.exists(metadata_file):
        print(f"  ✓ Metadata saved to: {metadata_file}")
        
        # Read it back
        import json
        with open(metadata_file, 'r') as f:
            loaded_metadata = json.load(f)
        
        print(f"  ✓ Metadata contains {len(loaded_metadata)} fields")
        print(f"  ✓ Title: {loaded_metadata.get('title')}")
        print(f"  ✓ Downloaded at: {loaded_metadata.get('downloaded_at')}")
    else:
        print("  ✗ Failed to save metadata")


def main():
    """Run all tests"""
    print("Instagram Downloader - Test Suite")
    print("=" * 40)
    
    # Ensure directories are created
    utils.create_directories()
    
    # Run tests
    test_url_validation()
    test_file_operations()
    test_directory_structure()
    test_urls_file()
    test_metadata_operations()
    
    print("\n" + "=" * 40)
    print("Test suite completed!")
    print("\nTo test actual downloading, add real Instagram URLs to 'urls.txt' and run:")
    print("  python3 ig_downloader.py -f urls.txt")
    print("\nOr download a single URL:")
    print("  python3 ig_downloader.py 'https://www.instagram.com/p/YOUR_POST_ID/'")


if __name__ == "__main__":
    main()
