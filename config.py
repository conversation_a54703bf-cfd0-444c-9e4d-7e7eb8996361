"""
Configuration settings for Instagram Downloader
"""
import os

# Base directories
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DOWNLOADS_DIR = os.path.join(BASE_DIR, "downloads")
IMAGES_DIR = os.path.join(DOWNLOADS_DIR, "images")
VIDEOS_DIR = os.path.join(DOWNLOADS_DIR, "videos")
METADATA_DIR = os.path.join(DOWNLOADS_DIR, "metadata")

# Download settings
MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds
TIMEOUT = 30  # seconds

# File naming
TIMESTAMP_FORMAT = "%Y%m%d_%H%M%S"
MAX_FILENAME_LENGTH = 100

# User agent for requests
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# yt-dlp options
YT_DLP_OPTIONS = {
    'format': 'best',
    'outtmpl': '%(title)s.%(ext)s',
    'writeinfojson': True,
    'writethumbnail': False,
    'writesubtitles': False,
    'writeautomaticsub': False,
    'ignoreerrors': True,
    'no_warnings': False,
    'extractaudio': False,
    'audioformat': 'mp3',
    'embed_subs': False,
    'writesubtitles': False,
}

# Supported file extensions
IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi', '.mkv', '.webm']

# Instagram URL patterns
INSTAGRAM_PATTERNS = [
    r'https?://(?:www\.)?instagram\.com/p/([^/?#&]+)',
    r'https?://(?:www\.)?instagram\.com/reel/([^/?#&]+)',
    r'https?://(?:www\.)?instagram\.com/tv/([^/?#&]+)',
]
