#!/usr/bin/env python3
"""
Instagram Bulk Downloader
Downloads Instagram posts, reels, and IGTV videos without login
Organizes content into separate folders for images and videos
"""

import os
import sys
import time
import json
import argparse
from typing import List, Dict, Optional
from pathlib import Path

import yt_dlp
import requests
from tqdm import tqdm
from colorama import init, Fore, Style

import config
import utils

# Initialize colorama for cross-platform colored output
init(autoreset=True)


class InstagramDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': config.USER_AGENT})
        self.downloaded_count = 0
        self.failed_count = 0
        
    def download_with_ytdlp(self, url: str, post_id: str) -> Dict:
        """Download using yt-dlp"""
        result = {
            'success': False,
            'files': [],
            'metadata': {},
            'error': None
        }
        
        # Configure yt-dlp options for this download
        ydl_opts = config.YT_DLP_OPTIONS.copy()
        ydl_opts['outtmpl'] = os.path.join(config.DOWNLOADS_DIR, f"{post_id}_%(title)s.%(ext)s")
        
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Extract info first
                info = ydl.extract_info(url, download=False)
                result['metadata'] = {
                    'title': info.get('title', ''),
                    'description': info.get('description', ''),
                    'uploader': info.get('uploader', ''),
                    'upload_date': info.get('upload_date', ''),
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'duration': info.get('duration', 0),
                    'url': url,
                    'post_id': post_id
                }
                
                # Download the content
                ydl.download([url])
                
                # Find downloaded files
                download_dir = Path(config.DOWNLOADS_DIR)
                for file_path in download_dir.glob(f"{post_id}_*"):
                    if file_path.is_file():
                        # Move to appropriate category folder
                        new_path = utils.move_file_to_category(str(file_path), post_id)
                        result['files'].append(new_path)
                
                result['success'] = True
                
        except Exception as e:
            result['error'] = str(e)
            print(f"{Fore.RED}yt-dlp error for {url}: {e}")
        
        return result
    
    def download_single_url(self, url: str) -> bool:
        """Download content from a single Instagram URL"""
        if not utils.validate_instagram_url(url):
            print(f"{Fore.RED}Invalid Instagram URL: {url}")
            return False
        
        post_id = utils.extract_post_id(url)
        if not post_id:
            print(f"{Fore.RED}Could not extract post ID from: {url}")
            return False
        
        print(f"{Fore.CYAN}Downloading: {url}")
        print(f"{Fore.YELLOW}Post ID: {post_id}")
        
        # Try downloading with yt-dlp
        result = self.download_with_ytdlp(url, post_id)
        
        if result['success']:
            # Save metadata
            if result['metadata']:
                utils.save_metadata(post_id, result['metadata'])
            
            # Report success
            file_count = len(result['files'])
            print(f"{Fore.GREEN}✓ Downloaded {file_count} file(s) for post {post_id}")
            
            for file_path in result['files']:
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                print(f"  - {os.path.basename(file_path)} ({utils.format_file_size(file_size)})")
            
            self.downloaded_count += 1
            return True
        else:
            print(f"{Fore.RED}✗ Failed to download: {url}")
            if result['error']:
                print(f"  Error: {result['error']}")
            self.failed_count += 1
            return False
    
    def download_bulk(self, urls: List[str]) -> Dict:
        """Download multiple URLs with progress tracking"""
        print(f"{Fore.CYAN}Starting bulk download of {len(urls)} URLs...")
        
        results = {
            'successful': [],
            'failed': [],
            'total': len(urls)
        }
        
        with tqdm(total=len(urls), desc="Downloading", unit="post") as pbar:
            for i, url in enumerate(urls, 1):
                pbar.set_description(f"Downloading {i}/{len(urls)}")
                
                success = self.download_single_url(url)
                
                if success:
                    results['successful'].append(url)
                else:
                    results['failed'].append(url)
                
                pbar.update(1)
                
                # Small delay between downloads to be respectful
                if i < len(urls):
                    time.sleep(1)
        
        return results
    
    def print_summary(self):
        """Print download summary"""
        total = self.downloaded_count + self.failed_count
        if total == 0:
            return
        
        print(f"\n{Fore.CYAN}{'='*50}")
        print(f"{Fore.CYAN}DOWNLOAD SUMMARY")
        print(f"{Fore.CYAN}{'='*50}")
        print(f"{Fore.GREEN}✓ Successful: {self.downloaded_count}")
        print(f"{Fore.RED}✗ Failed: {self.failed_count}")
        print(f"{Fore.YELLOW}Total: {total}")
        
        if self.downloaded_count > 0:
            print(f"\n{Fore.CYAN}Files saved to:")
            print(f"  Images: {config.IMAGES_DIR}")
            print(f"  Videos: {config.VIDEOS_DIR}")
            print(f"  Metadata: {config.METADATA_DIR}")


def main():
    parser = argparse.ArgumentParser(
        description="Instagram Bulk Downloader - Download Instagram content without login"
    )
    parser.add_argument(
        'urls',
        nargs='*',
        help='Instagram URLs to download'
    )
    parser.add_argument(
        '-f', '--file',
        help='File containing Instagram URLs (one per line)'
    )
    parser.add_argument(
        '--setup',
        action='store_true',
        help='Setup directories and create example files'
    )
    
    args = parser.parse_args()
    
    # Setup mode
    if args.setup:
        utils.create_directories()
        
        # Create example URLs file
        example_urls = [
            "# Instagram URLs - one per line",
            "# Lines starting with # are comments",
            "",
            "# Example URLs (replace with actual URLs):",
            "# https://www.instagram.com/p/EXAMPLE123/",
            "# https://www.instagram.com/reel/EXAMPLE456/",
        ]
        
        with open('urls.txt', 'w') as f:
            f.write('\n'.join(example_urls))
        
        print(f"{Fore.GREEN}✓ Setup complete!")
        print(f"{Fore.YELLOW}Edit 'urls.txt' and add your Instagram URLs")
        return
    
    # Create directories
    utils.create_directories()
    
    # Initialize downloader
    downloader = InstagramDownloader()
    
    # Collect URLs
    urls = []
    
    if args.file:
        urls.extend(utils.load_urls_from_file(args.file))
    
    if args.urls:
        urls.extend(args.urls)
    
    if not urls:
        print(f"{Fore.RED}No URLs provided!")
        print(f"{Fore.YELLOW}Usage:")
        print(f"  {sys.argv[0]} URL1 URL2 URL3...")
        print(f"  {sys.argv[0]} -f urls.txt")
        print(f"  {sys.argv[0]} --setup")
        return
    
    # Remove duplicates while preserving order
    urls = list(dict.fromkeys(urls))
    
    print(f"{Fore.CYAN}Instagram Bulk Downloader")
    print(f"{Fore.CYAN}{'='*30}")
    
    try:
        if len(urls) == 1:
            downloader.download_single_url(urls[0])
        else:
            downloader.download_bulk(urls)
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Download interrupted by user")
    except Exception as e:
        print(f"\n{Fore.RED}Unexpected error: {e}")
    finally:
        downloader.print_summary()


if __name__ == "__main__":
    main()
