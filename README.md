# Instagram Bulk Downloader

A powerful Python tool for bulk downloading Instagram posts, reels, and IGTV videos **without requiring login**. Automatically organizes downloaded content into separate folders for images and videos with subfolders for each post.

## Features

- ✅ **No Login Required** - Downloads public Instagram content without authentication
- 📁 **Organized Downloads** - Automatically creates subfolders for images and videos
- 🔄 **Bulk Processing** - Download multiple URLs at once from a file or command line
- 📊 **Progress Tracking** - Real-time progress bars and download statistics
- 🎯 **Multiple Formats** - Supports posts, reels, IGTV, and stories (if public)
- 📝 **Metadata Extraction** - Saves post information (title, description, likes, etc.)
- 🛡️ **Error Handling** - Robust error handling with retry logic
- 🎨 **Colored Output** - Beautiful colored terminal output

## Installation

1. **Clone or download this repository**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

### 1. Setup (First Time)
```bash
python ig_downloader.py --setup
```
This creates the necessary folders and an example `urls.txt` file.

### 2. Download Single URL
```bash
python ig_downloader.py "https://www.instagram.com/p/EXAMPLE123/"
```

### 3. Download Multiple URLs
```bash
python ig_downloader.py "URL1" "URL2" "URL3"
```

### 4. Bulk Download from File
```bash
# Edit urls.txt with your Instagram URLs
python ig_downloader.py -f urls.txt
```

## Folder Structure

After running the tool, your downloads will be organized as follows:

```
downloads/
├── images/
│   ├── POST_ID_1/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── POST_ID_2/
│       └── image.png
├── videos/
│   ├── POST_ID_3/
│   │   └── video.mp4
│   └── POST_ID_4/
│       └── reel.mp4
└── metadata/
    ├── POST_ID_1.json
    ├── POST_ID_2.json
    ├── POST_ID_3.json
    └── POST_ID_4.json
```

## Usage Examples

### Command Line Arguments
```bash
# Setup directories and example files
python ig_downloader.py --setup

# Download single post
python ig_downloader.py "https://www.instagram.com/p/ABC123/"

# Download multiple posts
python ig_downloader.py "URL1" "URL2" "URL3"

# Download from file
python ig_downloader.py -f my_urls.txt

# Get help
python ig_downloader.py --help
```

### URL File Format
Create a text file (e.g., `urls.txt`) with Instagram URLs:

```
# Instagram URLs - one per line
# Lines starting with # are comments

https://www.instagram.com/p/ABC123/
https://www.instagram.com/reel/DEF456/
https://www.instagram.com/tv/GHI789/

# You can add comments anywhere
https://www.instagram.com/p/JKL012/
```

## Supported URL Types

- **Posts**: `https://www.instagram.com/p/POST_ID/`
- **Reels**: `https://www.instagram.com/reel/REEL_ID/`
- **IGTV**: `https://www.instagram.com/tv/TV_ID/`

## Configuration

You can modify `config.py` to customize:

- Download directories
- File naming patterns
- Retry settings
- yt-dlp options
- User agent strings

## Metadata

For each downloaded post, the tool saves metadata in JSON format including:

- Post title and description
- Author/uploader information
- Upload date
- View count, like count
- Duration (for videos)
- Original URL and post ID

## Troubleshooting

### Common Issues

1. **"No module named 'yt_dlp'"**
   ```bash
   pip install yt-dlp
   ```

2. **Downloads failing**
   - Ensure URLs are public (not private accounts)
   - Check your internet connection
   - Some content may be geo-restricted

3. **Permission errors**
   - Make sure you have write permissions in the directory
   - Try running with appropriate permissions

### Error Handling

The tool includes robust error handling:
- Automatic retries for failed downloads
- Graceful handling of invalid URLs
- Detailed error messages
- Progress tracking continues even if some downloads fail

## Legal Notice

This tool is for educational and personal use only. Please respect Instagram's Terms of Service and copyright laws. Only download content you have permission to download.

## Requirements

- Python 3.7+
- Internet connection
- Dependencies listed in `requirements.txt`

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.

## License

This project is open source. Use responsibly and respect content creators' rights.
